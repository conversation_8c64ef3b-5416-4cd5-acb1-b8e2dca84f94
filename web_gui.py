from flask import Flask, render_template, request, jsonify, send_file
from database import Database
from config import Config
from browser_search import BaiduCrawler
from scheduler import task_scheduler
import threading
import json
import os
import shutil
import re

app = Flask(__name__)
db = Database()

# 创建配置表
def init_db():
    """初始化数据库表"""
    with db.get_connection() as conn:
        cursor = conn.cursor()
        # 创建配置表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS crawler_config (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                urls TEXT NOT NULL,
                created_at DATETIME NOT NULL
            )
        ''')
        
        # 如果表是空的，插入默认配置
        cursor.execute('SELECT COUNT(*) FROM crawler_config')
        if cursor.fetchone()[0] == 0:
            default_urls = json.dumps(Config.SEARCH_QUERIES, ensure_ascii=False)
            current_time = db.get_beijing_time()  # 使用统一的北京时间获取方法
            cursor.execute('''
                INSERT INTO crawler_config (name, urls, created_at)
                VALUES (?, ?, ?)
            ''', ('默认配置', default_urls, current_time))
        
        conn.commit()

def init_filter_tables():
    """初始化过滤相关的表结构"""
    with db.get_connection() as conn:
        cursor = conn.cursor()
        
        # 创建过滤配置表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS filter_configs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                description TEXT,
                enabled BOOLEAN NOT NULL DEFAULT 1,
                whitelist_required BOOLEAN NOT NULL DEFAULT 1,
                created_at DATETIME NOT NULL
            )
        ''')
        
        # 创建黑名单表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS filter_blacklist (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                config_id INTEGER NOT NULL,
                keyword TEXT NOT NULL,
                created_at DATETIME NOT NULL,
                FOREIGN KEY (config_id) REFERENCES filter_configs (id) ON DELETE CASCADE,
                UNIQUE(config_id, keyword)
            )
        ''')
        
        # 创建白名单表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS filter_whitelist (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                config_id INTEGER NOT NULL,
                keyword TEXT NOT NULL,
                created_at DATETIME NOT NULL,
                FOREIGN KEY (config_id) REFERENCES filter_configs (id) ON DELETE CASCADE,
                UNIQUE(config_id, keyword)
            )
        ''')
        
        conn.commit()

def migrate_filter_tables():
    """迁移过滤相关的表结构"""
    with db.get_connection() as conn:
        cursor = conn.cursor()
        
        # 检查是否存在旧表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='filter_config'")
        old_table_exists = cursor.fetchone() is not None
        
        if old_table_exists:
            print("检测到旧表 filter_config，开始迁移数据...")
            
            # 检查旧表结构
            cursor.execute("PRAGMA table_info(filter_config)")
            columns = [column[1] for column in cursor.fetchall()]
            print(f"旧表列结构: {columns}")
            
            # 创建新表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS filter_configs (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    description TEXT,
                    enabled BOOLEAN NOT NULL DEFAULT 1,
                    whitelist_required BOOLEAN NOT NULL DEFAULT 1,
                    created_at DATETIME NOT NULL
                )
            ''')
            
            # 根据旧表结构调整迁移语句
            if 'whitelist_required' in columns:
                # 如果旧表有whitelist_required列，直接迁移
                cursor.execute('''
                    INSERT OR IGNORE INTO filter_configs 
                    (id, name, description, enabled, whitelist_required, created_at)
                    SELECT id, name, description, enabled, whitelist_required, created_at 
                    FROM filter_config
                ''')
            elif 'whitelist_overrides' in columns:
                # 如果旧表有whitelist_overrides列，转换后迁移
                cursor.execute('''
                    INSERT OR IGNORE INTO filter_configs 
                    (id, name, description, enabled, whitelist_required, created_at)
                    SELECT id, name, description, enabled, whitelist_overrides, created_at 
                    FROM filter_config
                ''')
            else:
                # 如果旧表没有相关列，使用默认值
                cursor.execute('''
                    INSERT OR IGNORE INTO filter_configs 
                    (id, name, description, enabled, whitelist_required, created_at)
                    SELECT id, name, description, enabled, 1, created_at 
                    FROM filter_config
                ''')
            
            # 检查黑名单表（旧表结构，用于数据迁移）
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='blacklist_keywords'")
            blacklist_table_exists = cursor.fetchone() is not None
            
            # 创建黑名单表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS filter_blacklist (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    config_id INTEGER NOT NULL,
                    keyword TEXT NOT NULL,
                    created_at DATETIME NOT NULL,
                    FOREIGN KEY (config_id) REFERENCES filter_configs (id) ON DELETE CASCADE,
                    UNIQUE(config_id, keyword)
                )
            ''')
            
            # 迁移黑名单数据（一次性迁移，迁移后旧表可以删除）
            if blacklist_table_exists:
                cursor.execute('''
                    INSERT OR IGNORE INTO filter_blacklist (config_id, keyword, created_at)
                    SELECT config_id, keyword, created_at FROM blacklist_keywords
                ''')

            # 检查白名单表（旧表结构，用于数据迁移）
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='whitelist_keywords'")
            whitelist_table_exists = cursor.fetchone() is not None
            
            # 创建白名单表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS filter_whitelist (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    config_id INTEGER NOT NULL,
                    keyword TEXT NOT NULL,
                    created_at DATETIME NOT NULL,
                    FOREIGN KEY (config_id) REFERENCES filter_configs (id) ON DELETE CASCADE,
                    UNIQUE(config_id, keyword)
                )
            ''')
            
            # 迁移白名单数据（一次性迁移，迁移后旧表可以删除）
            if whitelist_table_exists:
                cursor.execute('''
                    INSERT OR IGNORE INTO filter_whitelist (config_id, keyword, created_at)
                    SELECT config_id, keyword, created_at FROM whitelist_keywords
                ''')
            
            conn.commit()
            print("数据迁移完成")

# 添加一个简单的日志存储
crawler_logs = []

# 添加爬虫实例存储
current_crawler = None

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/api/config', methods=['GET'])
def get_configs():
    """获取所有配置"""
    try:
        with db.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('SELECT * FROM crawler_config ORDER BY created_at DESC')
            configs = cursor.fetchall()
            
            result = []
            for c in configs:
                try:
                    urls = json.loads(c[2]) if c[2] else []
                except json.JSONDecodeError:
                    urls = []
                    
                result.append({
                    'id': c[0],
                    'name': c[1],
                    'urls': urls,
                    'created_at': c[3]
                })
            
            return jsonify(result)
    except Exception as e:
        print(f"获取配置失败: {str(e)}")  # 添加服务器端日志
        return jsonify([])

@app.route('/api/config', methods=['POST'])
def save_config():
    """保存配置"""
    try:
        data = request.get_json()
        name = data.get('name')
        urls = data.get('urls', [])
        
        if not name or not urls:
            return jsonify({'status': 'error', 'message': '配置名称和URL列表不能为空'}), 400
            
        with db.get_connection() as conn:
            cursor = conn.cursor()
            current_time = db.get_beijing_time()  # 获取北京时间
            cursor.execute(
                'INSERT INTO crawler_config (name, urls, created_at) VALUES (?, ?, ?)',
                (name, json.dumps(urls, ensure_ascii=False), current_time)
            )
            conn.commit()
            
        return jsonify({'status': 'success'})
    except Exception as e:
        return jsonify({'status': 'error', 'message': str(e)}), 500

@app.route('/api/start', methods=['POST'])
def start_crawler():
    """启动爬虫"""
    global current_crawler, crawler_logs
    
    # 清空之前的日志
    crawler_logs.clear()
    
    data = request.json
    Config.SEARCH_QUERIES = data['urls']
    
    def log_callback(msg):
        crawler_logs.append(msg)
    
    def run_crawler():
        global current_crawler
        try:
            # 获取文案过滤配置
            filter_config_id = data.get('filterConfigId')
            filter_config = None
            
            if filter_config_id:
                with db.get_connection() as conn:
                    cursor = conn.cursor()
                    cursor.execute('SELECT * FROM filter_configs WHERE id = ?', (filter_config_id,))
                    config = cursor.fetchone()
                    
                    if config:
                        # 获取黑名单
                        cursor.execute('SELECT keyword FROM filter_blacklist WHERE config_id = ?', (filter_config_id,))
                        blacklist = [row[0] for row in cursor.fetchall()]
                        
                        # 获取白名单
                        cursor.execute('SELECT keyword FROM filter_whitelist WHERE config_id = ?', (filter_config_id,))
                        whitelist = [row[0] for row in cursor.fetchall()]
                        
                        filter_config = {
                            'enabled': bool(config[3]),
                            'whitelist_required': bool(config[4]),
                            'blacklist': blacklist,
                            'whitelist': whitelist
                        }

            # 获取URL过滤配置
            url_filter_config_id = data.get('urlFilterConfigId')
            url_filter_config = None
            
            if url_filter_config_id:
                with db.get_connection() as conn:
                    cursor = conn.cursor()
                    cursor.execute('SELECT * FROM url_filter_configs WHERE id = ?', (url_filter_config_id,))
                    config = cursor.fetchone()
                    
                    if config:
                        # 获取黑名单规则
                        cursor.execute('SELECT pattern FROM url_blacklist WHERE config_id = ?', (url_filter_config_id,))
                        blacklist = [{'pattern': row[0]} for row in cursor.fetchall()]
                        
                        # 注意：url_whitelist表已被移除，因为实际功能中未使用
                        whitelist = []
                        
                        url_filter_config = {
                            'enabled': bool(config[3]),
                            'blacklist': blacklist,
                            'whitelist': whitelist
                        }
            
            crawler = BaiduCrawler(
                log_callback=log_callback,
                skip_duplicate=data.get('skipDuplicate', True),
                skip_task_duplicate=data.get('skipTaskDuplicate', True),
                filter_config=filter_config,
                url_filter_config=url_filter_config
            )
            current_crawler = crawler
            
            # 配置快照：保存本次POST的所有参数
            import json as _json
            config_snapshot = _json.dumps(data, ensure_ascii=False)
            # 确保执行记录被创建，带快照
            db.start_execution(crawler.execution_key, config_snapshot=config_snapshot)
            
            # 运行爬虫
            results = crawler.run(data['urls'])
            
            # 确保结果保存到数据库
            if results:
                db.save_results(results, crawler.execution_key)
                log_callback(f"已将 {len(results)} 条结果保存到数据库")
            else:
                log_callback("没有结果保存到数据库")
                
        finally:
            current_crawler = None
    
    # 启动爬虫线程
    thread = threading.Thread(target=run_crawler)
    thread.daemon = True
    thread.start()
    
    return jsonify({'status': 'success', 'message': '爬虫已启动'})

@app.route('/api/stop', methods=['POST'])
def stop_crawler():
    """停止爬虫"""
    global current_crawler
    if current_crawler:
        current_crawler.stop()
        current_crawler = None  # 清除当前爬虫实例
        return jsonify({'status': 'success', 'message': '爬虫已停止'})
    return jsonify({'status': 'error', 'message': '爬虫未在运行'})

@app.route('/api/status', methods=['GET'])
def get_crawler_status():
    """获取爬虫状态"""
    return jsonify({
        'running': current_crawler is not None
    })

@app.route('/api/config/<int:config_id>', methods=['GET'])
def get_config(config_id):
    """获取单个配置"""
    try:
        with db.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('SELECT * FROM crawler_config WHERE id = ?', (config_id,))
            config = cursor.fetchone()
            
            if not config:
                return jsonify({'status': 'error', 'message': '配置不存在'}), 404
                
            try:
                urls = json.loads(config[2]) if config[2] else []
            except json.JSONDecodeError:
                urls = []
                
            return jsonify({
                'id': config[0],
                'name': config[1],
                'urls': urls,
                'created_at': config[3]
            })
    except Exception as e:
        return jsonify({'status': 'error', 'message': str(e)}), 500

@app.route('/api/config/<int:config_id>', methods=['DELETE'])
def delete_config(config_id):
    """删除配置"""
    with db.get_connection() as conn:
        cursor = conn.cursor()
        cursor.execute('DELETE FROM crawler_config WHERE id = ?', (config_id,))
        conn.commit()
    return jsonify({'status': 'success'})

@app.route('/api/results', methods=['GET'])
def get_results():
    """获取抓取结果（分页和搜索）"""
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 20, type=int)
    title = request.args.get('title', '')
    full_title = request.args.get('full_title', '')
    url = request.args.get('url', '')
    execution_key = request.args.get('executionKey', '')
    start_date = request.args.get('start_date', '')  # 新增开始时间参数
    end_date = request.args.get('end_date', '')      # 新增结束时间参数
    search_url = request.args.get('search_url', '')
    
    offset = (page - 1) * per_page
    
    with db.get_connection() as conn:
        cursor = conn.cursor()
        
        # 构建查询条件
        conditions = []
        params = []
        
        if title:
            conditions.append("title LIKE ?")
            params.append(f"%{title}%")
        if full_title:
            conditions.append("full_title LIKE ?")
            params.append(f"%{full_title}%")
        if url:
            conditions.append("link LIKE ?")
            params.append(f"%{url}%")
        if execution_key:
            conditions.append("execution_key LIKE ?")
            params.append(f"%{execution_key}%")
        if start_date:  # 修改日期查询逻辑
            conditions.append("datetime(created_at) >= datetime(?)")
            params.append(start_date)
        if end_date:    # 添加结束时间查询
            conditions.append("datetime(created_at) <= datetime(?)")
            params.append(end_date)
        if search_url:
            conditions.append("search_url LIKE ?")
            params.append(f"%{search_url}%")
            
        where_clause = " AND ".join(conditions) if conditions else "1=1"
        
        # 获取总数
        cursor.execute(f'SELECT COUNT(*) FROM search_results WHERE {where_clause}', params)
        total = cursor.fetchone()[0]
        
        # 获取分页数据
        cursor.execute(f'''
            SELECT id, execution_key, title, full_title, link, abstract, created_at, search_url
            FROM search_results 
            WHERE {where_clause}
            ORDER BY created_at DESC 
            LIMIT ? OFFSET ?
        ''', params + [per_page, offset])
        
        results = cursor.fetchall()
        
        return jsonify({
            'total': total,
            'page': page,
            'per_page': per_page,
            'total_pages': (total + per_page - 1) // per_page,
            'results': [{
                'id': r[0],
                'execution_key': r[1],
                'title': r[2],
                'full_title': r[3],
                'link': r[4],
                'abstract': r[5],
                'created_at': r[6],
                'search_url': r[7]
            } for r in results]
        })

@app.route('/api/result/<int:result_id>', methods=['DELETE'])
def delete_result(result_id):
    """删除抓取结果"""
    with db.get_connection() as conn:
        cursor = conn.cursor()
        cursor.execute('DELETE FROM search_results WHERE id = ?', (result_id,))
        conn.commit()
    return jsonify({'status': 'success'})

@app.route('/api/logs')
def get_logs():
    """获取日志"""
    last_id = int(request.args.get('last_id', 0))
    # 返回上次请求之后的新日志
    new_logs = [
        {'id': i, 'message': log} 
        for i, log in enumerate(crawler_logs[last_id:], start=last_id)
    ]
    return jsonify({'logs': new_logs})

@app.route('/api/executions', methods=['GET'])
def get_executions():
    """获取所有爬取记录"""
    try:
        with db.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT e.execution_key, e.created_at, COUNT(r.id) as result_count
                FROM executions e
                LEFT JOIN search_results r ON e.execution_key = r.execution_key
                GROUP BY e.execution_key, e.created_at
                ORDER BY e.created_at DESC
            ''')
            executions = cursor.fetchall()
            
            return jsonify([{
                'execution_key': e[0],
                'created_at': e[1],
                'result_count': e[2]
            } for e in executions])
    except Exception as e:
        print(f"获取执行记录失败: {str(e)}")  # 添加服务器端日志
        return jsonify([])  # 出错时返回空列表

@app.route('/api/execution/<execution_key>/download', methods=['GET'])
def download_execution_results(execution_key):
    """下载指定执行记录的CSV文件"""
    try:
        # 检查文件是否存在
        file_path = os.path.join('save_data', execution_key, 'all_results.csv')
        if not os.path.exists(file_path):
            print(f"文件不存在: {file_path}")
            return jsonify({'status': 'error', 'message': '文件不存在'}), 404
        
        # 确保文件可读
        if not os.access(file_path, os.R_OK):
            print(f"文件无法读取: {file_path}")
            return jsonify({'status': 'error', 'message': '文件无法读取'}), 403
            
        try:
            return send_file(
                file_path,
                mimetype='text/csv',
                as_attachment=True,
                download_name=f'{execution_key}_results.csv'
            )
        except Exception as e:
            print(f"发送文件失败: {str(e)}")
            return jsonify({'status': 'error', 'message': f'发送文件失败: {str(e)}'}), 500
    except Exception as e:
        print(f"下载文件时出错: {str(e)}")
        return jsonify({'status': 'error', 'message': str(e)}), 500

@app.route('/api/execution/<execution_key>', methods=['DELETE'])
def delete_execution(execution_key):
    """删除执行记录及其结果"""
    try:
        with db.get_connection() as conn:
            cursor = conn.cursor()
            # 删除搜索结果
            cursor.execute('DELETE FROM search_results WHERE execution_key = ?', (execution_key,))
            # 删除执行记录
            cursor.execute('DELETE FROM executions WHERE execution_key = ?', (execution_key,))
            conn.commit()
            
        # 删除保存的文件
        file_path = os.path.join('save_data', execution_key)
        if os.path.exists(file_path):
            shutil.rmtree(file_path)
            
        return jsonify({'status': 'success'})
    except Exception as e:
        return jsonify({'status': 'error', 'message': str(e)}), 500

# 添加过滤配置相关的API路由
@app.route('/api/filter-configs', methods=['GET'])
def get_filter_configs():
    """获取所有过滤配置"""
    try:
        with db.get_connection() as conn:
            cursor = conn.cursor()
            
            # 获取所有配置
            cursor.execute('SELECT * FROM filter_configs ORDER BY created_at DESC')
            configs = cursor.fetchall()
            
            result = []
            for c in configs:
                config_id = c[0]
                
                # 获取黑名单数量
                cursor.execute('SELECT COUNT(*) FROM filter_blacklist WHERE config_id = ?', (config_id,))
                blacklist_count = cursor.fetchone()[0]
                
                # 获取白名单数量
                cursor.execute('SELECT COUNT(*) FROM filter_whitelist WHERE config_id = ?', (config_id,))
                whitelist_count = cursor.fetchone()[0]
                
                result.append({
                    'id': config_id,
                    'name': c[1],
                    'description': c[2],
                    'enabled': bool(c[3]),
                    'whitelist_required': bool(c[4]),
                    'created_at': c[5],
                    'blacklist_count': blacklist_count,
                    'whitelist_count': whitelist_count
                })
            
            return jsonify(result)
    except Exception as e:
        print(f"获取过滤配置失败: {str(e)}")
        return jsonify([])

@app.route('/api/filter-config', methods=['POST'])
def create_filter_config():
    """创建新的过滤配置"""
    try:
        data = request.get_json()
        name = data.get('name')
        description = data.get('description', '')
        enabled = data.get('enabled', True)
        whitelist_required = data.get('whitelist_required', True)
        
        if not name:
            return jsonify({'status': 'error', 'message': '配置名称不能为空'}), 400
            
        with db.get_connection() as conn:
            cursor = conn.cursor()
            # 创建过滤配置表（如果不存在）
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS filter_configs (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    description TEXT,
                    enabled BOOLEAN NOT NULL DEFAULT 1,
                    whitelist_required BOOLEAN NOT NULL DEFAULT 1,
                    created_at DATETIME NOT NULL
                )
            ''')
            
            # 创建黑名单表（如果不存在）
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS filter_blacklist (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    config_id INTEGER NOT NULL,
                    keyword TEXT NOT NULL,
                    created_at DATETIME NOT NULL,
                    FOREIGN KEY (config_id) REFERENCES filter_configs (id) ON DELETE CASCADE
                )
            ''')
            
            # 创建白名单表（如果不存在）
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS filter_whitelist (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    config_id INTEGER NOT NULL,
                    keyword TEXT NOT NULL,
                    created_at DATETIME NOT NULL,
                    FOREIGN KEY (config_id) REFERENCES filter_configs (id) ON DELETE CASCADE
                )
            ''')
            
            current_time = db.get_beijing_time()
            cursor.execute(
                'INSERT INTO filter_configs (name, description, enabled, whitelist_required, created_at) VALUES (?, ?, ?, ?, ?)',
                (name, description, 1 if enabled else 0, 1 if whitelist_required else 0, current_time)
            )
            conn.commit()
            
            # 获取新创建的配置ID
            config_id = cursor.lastrowid
            
        return jsonify({'status': 'success', 'id': config_id})
    except Exception as e:
        return jsonify({'status': 'error', 'message': str(e)}), 500

@app.route('/api/filter-config/<int:config_id>', methods=['GET'])
def get_filter_config(config_id):
    """获取单个过滤配置详情"""
    try:
        with db.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('SELECT * FROM filter_configs WHERE id = ?', (config_id,))
            config = cursor.fetchone()
            
            if not config:
                print(f"配置不存在: ID={config_id}")
                return jsonify({'status': 'error', 'message': '配置不存在'}), 404
            
            print(f"获取配置: ID={config_id}, 名称={config[1]}")
                
            # 获取黑名单
            cursor.execute('SELECT keyword FROM filter_blacklist WHERE config_id = ?', (config_id,))
            blacklist = [row[0] for row in cursor.fetchall()]
            print(f"黑名单关键词数量: {len(blacklist)}")
            
            # 获取白名单
            cursor.execute('SELECT keyword FROM filter_whitelist WHERE config_id = ?', (config_id,))
            whitelist = [row[0] for row in cursor.fetchall()]
            print(f"白名单关键词数量: {len(whitelist)}")
                
            result = {
                'id': config[0],
                'name': config[1],
                'description': config[2],
                'enabled': bool(config[3]),
                'whitelist_required': bool(config[4]),
                'created_at': config[5],
                'blacklist': blacklist,
                'whitelist': whitelist
            }
            print(f"返回结果: {result}")
            return jsonify(result)
    except Exception as e:
        print(f"获取过滤配置详情失败: {str(e)}")
        return jsonify({'status': 'error', 'message': str(e)}), 500

@app.route('/api/filter-config/<int:config_id>', methods=['PUT'])
def update_filter_config(config_id):
    """更新过滤配置"""
    try:
        data = request.get_json()
        name = data.get('name')
        description = data.get('description')
        enabled = data.get('enabled')
        whitelist_required = data.get('whitelist_required')
        
        if not name:
            return jsonify({'status': 'error', 'message': '配置名称不能为空'}), 400
            
        with db.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('SELECT id FROM filter_configs WHERE id = ?', (config_id,))
            if not cursor.fetchone():
                return jsonify({'status': 'error', 'message': '配置不存在'}), 404
                
            cursor.execute(
                'UPDATE filter_configs SET name = ?, description = ?, enabled = ?, whitelist_required = ? WHERE id = ?',
                (name, description, 1 if enabled else 0, 1 if whitelist_required else 0, config_id)
            )
            conn.commit()
            
        return jsonify({'status': 'success'})
    except Exception as e:
        return jsonify({'status': 'error', 'message': str(e)}), 500

@app.route('/api/filter-config/<int:config_id>', methods=['DELETE'])
def delete_filter_config(config_id):
    """删除过滤配置"""
    try:
        with db.get_connection() as conn:
            cursor = conn.cursor()
            # 检查配置是否存在
            cursor.execute('SELECT id FROM filter_configs WHERE id = ?', (config_id,))
            if not cursor.fetchone():
                return jsonify({'status': 'error', 'message': '配置不存在'}), 404
                
            # 删除配置（级联删除会自动删除相关的黑白名单）
            cursor.execute('DELETE FROM filter_configs WHERE id = ?', (config_id,))
            conn.commit()
            
        return jsonify({'status': 'success'})
    except Exception as e:
        return jsonify({'status': 'error', 'message': str(e)}), 500

@app.route('/api/filter-config/<int:config_id>/blacklist', methods=['POST'])
def add_to_blacklist(config_id):
    """添加关键词到黑名单"""
    try:
        data = request.get_json()
        keyword = data.get('keyword')
        
        if not keyword:
            return jsonify({'status': 'error', 'message': '关键词不能为空'}), 400
            
        with db.get_connection() as conn:
            cursor = conn.cursor()
            # 检查配置是否存在
            cursor.execute('SELECT id FROM filter_configs WHERE id = ?', (config_id,))
            if not cursor.fetchone():
                return jsonify({'status': 'error', 'message': '配置不存在'}), 404
                
            # 检查关键词是否已存在
            cursor.execute('SELECT id FROM filter_blacklist WHERE config_id = ? AND keyword = ?', (config_id, keyword))
            if cursor.fetchone():
                return jsonify({'status': 'error', 'message': '关键词已存在'}), 400
                
            # 添加关键词
            current_time = db.get_beijing_time()
            cursor.execute(
                'INSERT INTO filter_blacklist (config_id, keyword, created_at) VALUES (?, ?, ?)',
                (config_id, keyword, current_time)
            )
            conn.commit()
            
        return jsonify({'status': 'success'})
    except Exception as e:
        return jsonify({'status': 'error', 'message': str(e)}), 500

@app.route('/api/filter-config/<int:config_id>/blacklist', methods=['DELETE'])
def remove_from_blacklist(config_id):
    """从黑名单移除关键词"""
    try:
        data = request.get_json()
        keyword = data.get('keyword')
        
        if not keyword:
            return jsonify({'status': 'error', 'message': '关键词不能为空'}), 400
            
        with db.get_connection() as conn:
            cursor = conn.cursor()
            # 删除关键词
            cursor.execute(
                'DELETE FROM filter_blacklist WHERE config_id = ? AND keyword = ?',
                (config_id, keyword)
            )
            conn.commit()
            
        return jsonify({'status': 'success'})
    except Exception as e:
        return jsonify({'status': 'error', 'message': str(e)}), 500

@app.route('/api/filter-config/<int:config_id>/whitelist', methods=['POST'])
def add_to_whitelist(config_id):
    """添加关键词到白名单"""
    try:
        data = request.get_json()
        keyword = data.get('keyword')
        
        if not keyword:
            return jsonify({'status': 'error', 'message': '关键词不能为空'}), 400
            
        with db.get_connection() as conn:
            cursor = conn.cursor()
            # 检查配置是否存在
            cursor.execute('SELECT id FROM filter_configs WHERE id = ?', (config_id,))
            if not cursor.fetchone():
                return jsonify({'status': 'error', 'message': '配置不存在'}), 404
                
            # 检查关键词是否已存在
            cursor.execute('SELECT id FROM filter_whitelist WHERE config_id = ? AND keyword = ?', (config_id, keyword))
            if cursor.fetchone():
                return jsonify({'status': 'error', 'message': '关键词已存在'}), 400
                
            # 添加关键词
            current_time = db.get_beijing_time()
            cursor.execute(
                'INSERT INTO filter_whitelist (config_id, keyword, created_at) VALUES (?, ?, ?)',
                (config_id, keyword, current_time)
            )
            conn.commit()
            
        return jsonify({'status': 'success'})
    except Exception as e:
        return jsonify({'status': 'error', 'message': str(e)}), 500

@app.route('/api/filter-config/<int:config_id>/whitelist', methods=['DELETE'])
def remove_from_whitelist(config_id):
    """从白名单移除关键词"""
    try:
        data = request.get_json()
        keyword = data.get('keyword')
        
        if not keyword:
            return jsonify({'status': 'error', 'message': '关键词不能为空'}), 400
            
        with db.get_connection() as conn:
            cursor = conn.cursor()
            # 删除关键词
            cursor.execute(
                'DELETE FROM filter_whitelist WHERE config_id = ? AND keyword = ?',
                (config_id, keyword)
            )
            conn.commit()
            
        return jsonify({'status': 'success'})
    except Exception as e:
        return jsonify({'status': 'error', 'message': str(e)}), 500

# 添加批量添加黑名单关键词的API
@app.route('/api/filter-config/<int:config_id>/blacklist/batch', methods=['POST'])
def batch_add_to_blacklist(config_id):
    """批量添加关键词到黑名单"""
    try:
        data = request.get_json()
        keywords = data.get('keywords', [])
        
        if not keywords:
            return jsonify({'status': 'error', 'message': '关键词列表不能为空'}), 400
            
        with db.get_connection() as conn:
            cursor = conn.cursor()
            # 检查配置是否存在
            cursor.execute('SELECT id FROM filter_configs WHERE id = ?', (config_id,))
            if not cursor.fetchone():
                return jsonify({'status': 'error', 'message': '配置不存在'}), 404
            
            # 获取现有关键词
            cursor.execute('SELECT keyword FROM filter_blacklist WHERE config_id = ?', (config_id,))
            existing_keywords = {row[0] for row in cursor.fetchall()}
            
            # 过滤掉已存在的关键词
            new_keywords = [k for k in keywords if k and k not in existing_keywords]
            
            if not new_keywords:
                return jsonify({'status': 'success', 'message': '没有新的关键词需要添加'})
            
            # 批量添加关键词
            current_time = db.get_beijing_time()
            values = [(config_id, keyword, current_time) for keyword in new_keywords]
            cursor.executemany(
                'INSERT INTO filter_blacklist (config_id, keyword, created_at) VALUES (?, ?, ?)',
                values
            )
            conn.commit()
            
        return jsonify({
            'status': 'success', 
            'added_count': len(new_keywords),
            'skipped_count': len(keywords) - len(new_keywords)
        })
    except Exception as e:
        return jsonify({'status': 'error', 'message': str(e)}), 500

# 添加批量添加白名单关键词的API
@app.route('/api/filter-config/<int:config_id>/whitelist/batch', methods=['POST'])
def batch_add_to_whitelist(config_id):
    """批量添加关键词到白名单"""
    try:
        data = request.get_json()
        keywords = data.get('keywords', [])
        
        if not keywords:
            return jsonify({'status': 'error', 'message': '关键词列表不能为空'}), 400
            
        with db.get_connection() as conn:
            cursor = conn.cursor()
            # 检查配置是否存在
            cursor.execute('SELECT id FROM filter_configs WHERE id = ?', (config_id,))
            if not cursor.fetchone():
                return jsonify({'status': 'error', 'message': '配置不存在'}), 404
            
            # 获取现有关键词
            cursor.execute('SELECT keyword FROM filter_whitelist WHERE config_id = ?', (config_id,))
            existing_keywords = {row[0] for row in cursor.fetchall()}
            
            # 过滤掉已存在的关键词
            new_keywords = [k for k in keywords if k and k not in existing_keywords]
            
            if not new_keywords:
                return jsonify({'status': 'success', 'message': '没有新的关键词需要添加'})
            
            # 批量添加关键词
            current_time = db.get_beijing_time()
            values = [(config_id, keyword, current_time) for keyword in new_keywords]
            cursor.executemany(
                'INSERT INTO filter_whitelist (config_id, keyword, created_at) VALUES (?, ?, ?)',
                values
            )
            conn.commit()
            
        return jsonify({
            'status': 'success', 
            'added_count': len(new_keywords),
            'skipped_count': len(keywords) - len(new_keywords)
        })
    except Exception as e:
        return jsonify({'status': 'error', 'message': str(e)}), 500

# 修改配置名称和描述的API
@app.route('/api/filter-config/<int:config_id>/meta', methods=['PUT'])
def update_filter_config_meta(config_id):
    """更新过滤配置的名称和描述"""
    try:
        data = request.get_json()
        name = data.get('name')
        description = data.get('description')
        
        if not name:
            return jsonify({'status': 'error', 'message': '配置名称不能为空'}), 400
            
        with db.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('SELECT id FROM filter_configs WHERE id = ?', (config_id,))
            if not cursor.fetchone():
                return jsonify({'status': 'error', 'message': '配置不存在'}), 404
                
            cursor.execute(
                'UPDATE filter_configs SET name = ?, description = ? WHERE id = ?',
                (name, description, config_id)
            )
            conn.commit()
            
        return jsonify({'status': 'success'})
    except Exception as e:
        return jsonify({'status': 'error', 'message': str(e)}), 500

@app.route('/api/url-blacklist', methods=['GET'])
def get_url_blacklist():
    """获取URL黑名单列表"""
    try:
        with db.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('SELECT id, pattern, description, created_at FROM url_blacklist ORDER BY created_at DESC')
            blacklist = cursor.fetchall()
            
            return jsonify([{
                'id': item[0],
                'pattern': item[1],
                'description': item[2],
                'created_at': item[3]
            } for item in blacklist])
    except Exception as e:
        return jsonify({'status': 'error', 'message': str(e)}), 500

@app.route('/api/url-whitelist', methods=['GET'])
def get_url_whitelist():
    """获取URL白名单列表"""
    try:
        with db.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('SELECT id, domain, description, created_at FROM url_whitelist ORDER BY created_at DESC')
            whitelist = cursor.fetchall()
            
            return jsonify([{
                'id': item[0],
                'domain': item[1],
                'description': item[2],
                'created_at': item[3]
            } for item in whitelist])
    except Exception as e:
        return jsonify({'status': 'error', 'message': str(e)}), 500

@app.route('/api/url-blacklist', methods=['POST'])
def add_url_blacklist():
    """添加URL黑名单"""
    try:
        data = request.get_json()
        pattern = data.get('pattern')
        description = data.get('description', '')
        
        if not pattern:
            return jsonify({'status': 'error', 'message': '正则表达式不能为空'}), 400
            
        # 验证正则表达式是否有效
        try:
            re.compile(pattern)
        except re.error:
            return jsonify({'status': 'error', 'message': '无效的正则表达式'}), 400
            
        db.add_url_blacklist(pattern, description)
        return jsonify({'status': 'success'})
    except Exception as e:
        return jsonify({'status': 'error', 'message': str(e)}), 500

@app.route('/api/url-whitelist', methods=['POST'])
def add_url_whitelist():
    """添加URL白名单"""
    try:
        data = request.get_json()
        domain = data.get('domain')
        description = data.get('description', '')
        
        if not domain:
            return jsonify({'status': 'error', 'message': '域名不能为空'}), 400
            
        db.add_url_whitelist(domain, description)
        return jsonify({'status': 'success'})
    except Exception as e:
        return jsonify({'status': 'error', 'message': str(e)}), 500

@app.route('/api/url-blacklist/<int:id>', methods=['DELETE'])
def delete_url_blacklist(id):
    """删除URL黑名单"""
    try:
        db.delete_url_blacklist(id)
        return jsonify({'status': 'success'})
    except Exception as e:
        return jsonify({'status': 'error', 'message': str(e)}), 500

@app.route('/api/url-whitelist/<int:id>', methods=['DELETE'])
def delete_url_whitelist(id):
    """删除URL白名单"""
    try:
        db.delete_url_whitelist(id)
        return jsonify({'status': 'success'})
    except Exception as e:
        return jsonify({'status': 'error', 'message': str(e)}), 500

@app.route('/api/url-filter-configs', methods=['GET'])
def get_url_filter_configs():
    """获取所有URL过滤配置"""
    try:
        configs = db.get_url_filter_configs()
        return jsonify([{
            'id': c[0],
            'name': c[1],
            'description': c[2],
            'enabled': bool(c[3]),
            'created_at': c[4]
        } for c in configs])
    except Exception as e:
        return jsonify({'status': 'error', 'message': str(e)}), 500

@app.route('/api/url-filter-config', methods=['POST'])
def create_url_filter_config():
    """创建URL过滤配置"""
    try:
        data = request.get_json()
        name = data.get('name')
        description = data.get('description', '')
        enabled = data.get('enabled', True)
        
        if not name:
            return jsonify({'status': 'error', 'message': '配置名称不能为空'}), 400
            
        config_id = db.create_url_filter_config(name, description, enabled)
        return jsonify({'status': 'success', 'id': config_id})
    except Exception as e:
        return jsonify({'status': 'error', 'message': str(e)}), 500

@app.route('/api/url-filter-config/<int:config_id>', methods=['GET'])
def get_url_filter_config(config_id):
    """获取URL过滤配置详情"""
    try:
        config = db.get_url_filter_config(config_id)
        if not config:
            return jsonify({
                'id': config_id,
                'name': '',
                'enabled': False,
                'blacklist': [],
                'whitelist': []
            })
        return jsonify(config)
    except Exception as e:
        print(f"获取URL过滤配置详情失败: {str(e)}")
        return jsonify({
            'id': config_id,
            'name': '',
            'enabled': False,
            'blacklist': [],
            'whitelist': []
        }), 500

@app.route('/api/url-filter-config/<int:config_id>', methods=['PUT'])
def update_url_filter_config(config_id):
    """更新URL过滤配置"""
    try:
        data = request.get_json()
        name = data.get('name')
        description = data.get('description', '')
        enabled = data.get('enabled', True)
        
        if not name:
            return jsonify({'status': 'error', 'message': '配置名称不能为空'}), 400
            
        db.update_url_filter_config(config_id, name, description, enabled)
        return jsonify({'status': 'success'})
    except Exception as e:
        return jsonify({'status': 'error', 'message': str(e)}), 500

@app.route('/api/url-filter-config/<int:config_id>', methods=['DELETE'])
def delete_url_filter_config(config_id):
    """删除URL过滤配置"""
    try:
        db.delete_url_filter_config(config_id)
        return jsonify({'status': 'success'})
    except Exception as e:
        return jsonify({'status': 'error', 'message': str(e)}), 500

@app.route('/api/url-filter-config/<int:config_id>/blacklist/batch', methods=['POST'])
def batch_add_url_blacklist(config_id):
    """批量添加URL黑名单规则"""
    try:
        data = request.get_json()
        patterns = data.get('patterns', [])
        
        if not patterns:
            return jsonify({'status': 'error', 'message': '规则列表不能为空'}), 400
            
        # 验证所有正则表达式
        for pattern in patterns:
            try:
                re.compile(pattern)
            except re.error:
                return jsonify({'status': 'error', 'message': f'无效的正则表达式: {pattern}'}), 400
        
        # 使用数据库方法批量添加
        added_count = db.batch_add_url_blacklist(config_id, patterns)
        
        return jsonify({
            'status': 'success',
            'added_count': added_count,
            'skipped_count': len(patterns) - added_count
        })
    except Exception as e:
        return jsonify({'status': 'error', 'message': str(e)}), 500

@app.route('/api/url-filter-config/<int:config_id>/whitelist/batch', methods=['POST'])
def batch_add_url_whitelist(config_id):
    """批量添加URL白名单规则"""
    try:
        data = request.get_json()
        domains = data.get('domains', [])
        
        if not domains:
            return jsonify({'status': 'error', 'message': '域名列表不能为空'}), 400
        
        # 使用数据库方法批量添加
        added_count = db.batch_add_url_whitelist(config_id, domains)
        
        return jsonify({
            'status': 'success',
            'added_count': added_count,
            'skipped_count': len(domains) - added_count
        })
    except Exception as e:
        return jsonify({'status': 'error', 'message': str(e)}), 500

# 添加URL黑名单规则
@app.route('/api/url-filter-config/<int:config_id>/blacklist', methods=['POST'])
def add_url_blacklist_rule(config_id):
    """添加URL黑名单规则"""
    try:
        data = request.get_json()
        pattern = data.get('pattern')
        description = data.get('description', '')
        
        if not pattern:
            return jsonify({'status': 'error', 'message': '正则表达式不能为空'}), 400
            
        # 验证正则表达式是否有效
        try:
            re.compile(pattern)
        except re.error as e:
            return jsonify({'status': 'error', 'message': f'无效的正则表达式: {str(e)}'}), 400
        
        # 检查配置是否存在
        with db.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('SELECT id FROM url_filter_configs WHERE id = ?', (config_id,))
            if not cursor.fetchone():
                return jsonify({'status': 'error', 'message': '配置不存在'}), 404
        
        # 添加规则
        try:
            db.add_url_blacklist(config_id, pattern, description)
            return jsonify({'status': 'success'})
        except Exception as e:
            return jsonify({'status': 'error', 'message': str(e)}), 500
            
    except Exception as e:
        return jsonify({'status': 'error', 'message': str(e)}), 500

# 删除URL黑名单规则
@app.route('/api/url-filter-config/<int:config_id>/blacklist/<int:rule_id>', methods=['DELETE'])
def delete_url_blacklist_rule(config_id, rule_id):
    """删除URL黑名单规则"""
    try:
        with db.get_connection() as conn:
            cursor = conn.cursor()
            # 检查规则是否存在且属于该配置
            cursor.execute('''
                DELETE FROM url_blacklist 
                WHERE id = ? AND config_id = ?
            ''', (rule_id, config_id))
            conn.commit()
            
            if cursor.rowcount > 0:
                return jsonify({'status': 'success'})
            else:
                return jsonify({'status': 'error', 'message': '规则不存在或不属于该配置'}), 404
    except Exception as e:
        return jsonify({'status': 'error', 'message': str(e)}), 500

# 批量添加URL黑名单规则
@app.route('/api/url-filter-config/<int:config_id>/blacklist/batch', methods=['POST'])
def batch_add_url_blacklist_rules(config_id):
    """批量添加URL黑名单规则"""
    try:
        data = request.get_json()
        patterns = data.get('patterns', [])
        
        if not patterns:
            return jsonify({'status': 'error', 'message': '规则列表不能为空'}), 400
            
        # 验证所有正则表达式
        invalid_patterns = []
        for pattern in patterns:
            try:
                re.compile(pattern)
            except re.error:
                invalid_patterns.append(pattern)
        
        if invalid_patterns:
            return jsonify({
                'status': 'error',
                'message': f'存在无效的正则表达式: {", ".join(invalid_patterns)}'
            }), 400
        
        # 添加规则
        added_count = db.batch_add_url_blacklist(config_id, patterns)
        
        return jsonify({
            'status': 'success',
            'added_count': added_count,
            'skipped_count': len(patterns) - added_count
        })
    except Exception as e:
        return jsonify({'status': 'error', 'message': str(e)}), 500

# 删除URL白名单规则
@app.route('/api/url-filter-config/<int:config_id>/whitelist/<int:rule_id>', methods=['DELETE'])
def delete_url_whitelist_rule(config_id, rule_id):
    """删除URL白名单规则"""
    try:
        with db.get_connection() as conn:
            cursor = conn.cursor()
            # 检查规则是否存在且属于该配置
            cursor.execute('''
                DELETE FROM url_whitelist 
                WHERE id = ? AND config_id = ?
            ''', (rule_id, config_id))
            conn.commit()
            
            if cursor.rowcount > 0:
                return jsonify({'status': 'success'})
            else:
                return jsonify({'status': 'error', 'message': '规则不存在或不属于该配置'}), 404
    except Exception as e:
        return jsonify({'status': 'error', 'message': str(e)}), 500

@app.route('/api/execution/<execution_key>/config', methods=['GET'])
def get_execution_config_snapshot(execution_key):
    """获取某次执行的配置快照"""
    config_snapshot = db.get_execution_config(execution_key)
    if config_snapshot:
        from flask import Response
        return Response(config_snapshot, mimetype='application/json')
    else:
        return jsonify({'status': 'error', 'message': '未找到配置快照'}), 404

# 定时任务相关API
@app.route('/api/scheduled-tasks', methods=['GET'])
def get_scheduled_tasks():
    """获取所有定时任务"""
    try:
        tasks = db.get_scheduled_tasks()
        result = []
        for task in tasks:
            schedule_config = json.loads(task[4]) if task[4] else {}
            result.append({
                'id': task[0],
                'name': task[1],
                'config_id': task[2],
                'config_name': task[13] if len(task) > 13 else '',  # config_name
                'schedule_type': task[3],
                'schedule_config': schedule_config,
                'enabled': bool(task[5]),
                'filter_config_id': task[6],
                'url_filter_config_id': task[7],
                'skip_duplicate': bool(task[8]),
                'skip_task_duplicate': bool(task[9]),
                'created_at': task[10],
                'updated_at': task[11],
                'status': task_scheduler.get_task_status(task[0])
            })
        return jsonify(result)
    except Exception as e:
        print(f"获取定时任务失败: {str(e)}")
        return jsonify([])

@app.route('/api/scheduled-tasks', methods=['POST'])
def create_scheduled_task():
    """创建定时任务"""
    try:
        data = request.get_json()
        name = data.get('name')
        config_id = data.get('config_id')
        schedule_type = data.get('schedule_type')
        schedule_config = data.get('schedule_config', {})
        filter_config_id = data.get('filter_config_id')
        url_filter_config_id = data.get('url_filter_config_id')
        skip_duplicate = data.get('skip_duplicate', True)
        skip_task_duplicate = data.get('skip_task_duplicate', True)

        if not name or not config_id or not schedule_type:
            return jsonify({'status': 'error', 'message': '任务名称、配置ID和调度类型不能为空'}), 400

        task_id = task_scheduler.add_task(
            name, config_id, schedule_type, schedule_config,
            filter_config_id, url_filter_config_id,
            skip_duplicate, skip_task_duplicate
        )

        return jsonify({'status': 'success', 'task_id': task_id})
    except Exception as e:
        return jsonify({'status': 'error', 'message': str(e)}), 500

@app.route('/api/scheduled-tasks/<int:task_id>', methods=['PUT'])
def update_scheduled_task_status(task_id):
    """更新定时任务状态"""
    try:
        data = request.get_json()
        enabled = data.get('enabled', True)

        task_scheduler.update_task_status(task_id, enabled)

        return jsonify({'status': 'success'})
    except Exception as e:
        return jsonify({'status': 'error', 'message': str(e)}), 500

@app.route('/api/scheduled-tasks/<int:task_id>', methods=['DELETE'])
def delete_scheduled_task(task_id):
    """删除定时任务"""
    try:
        task_scheduler.delete_task(task_id)
        return jsonify({'status': 'success'})
    except Exception as e:
        return jsonify({'status': 'error', 'message': str(e)}), 500

# 注意：scheduled_executions相关的API已被移除，因为实际功能中未使用
# 原来的get_scheduled_executions函数已被移除

# 启动定时任务调度器
with app.app_context():
    init_db()
    init_filter_tables()
    migrate_filter_tables()
    # 启动调度器
    task_scheduler.start()

if __name__ == '__main__':
    app.run(debug=True, port=5000)