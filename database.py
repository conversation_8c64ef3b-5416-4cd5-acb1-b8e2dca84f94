import sqlite3
from datetime import datetime, timedelta

class Database:
    def __init__(self, db_file='crawler.db'):
        """初始化数据库连接"""
        self.db_file = db_file
        self.init_db()

    def init_db(self):
        """初始化数据库表"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            # 检查executions表是否有config_snapshot字段，没有则添加
            cursor.execute("PRAGMA table_info(executions)")
            columns = [col[1] for col in cursor.fetchall()]
            if 'config_snapshot' not in columns:
                try:
                    cursor.execute('ALTER TABLE executions ADD COLUMN config_snapshot TEXT')
                except Exception as e:
                    print(f"添加config_snapshot字段失败: {e}")
            
            # 创建执行记录表（新建时包含config_snapshot）
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS executions (
                    execution_key TEXT PRIMARY KEY,
                    created_at DATETIME NOT NULL,
                    config_snapshot TEXT
                )
            ''')
            
            # 创建搜索结果表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS search_results (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    execution_key TEXT NOT NULL,
                    title TEXT,
                    full_title TEXT,
                    link TEXT,
                    abstract TEXT,
                    publish_unit TEXT,
                    search_url TEXT,
                    wd TEXT,
                    created_at DATETIME NOT NULL,
                    FOREIGN KEY (execution_key) REFERENCES executions (execution_key)
                )
            ''')
            
            # 创建配置表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS crawler_config (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    urls TEXT NOT NULL,
                    created_at DATETIME NOT NULL
                )
            ''')

            # 创建定时任务表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS scheduled_tasks (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    config_id INTEGER NOT NULL,
                    schedule_type TEXT NOT NULL,  -- 'daily', 'weekly', 'interval'
                    schedule_config TEXT NOT NULL,  -- JSON格式的调度配置
                    enabled BOOLEAN DEFAULT 1,
                    filter_config_id INTEGER,
                    url_filter_config_id INTEGER,
                    skip_duplicate BOOLEAN DEFAULT 1,
                    skip_task_duplicate BOOLEAN DEFAULT 1,
                    created_at DATETIME NOT NULL,
                    updated_at DATETIME NOT NULL,
                    FOREIGN KEY (config_id) REFERENCES crawler_config (id)
                )
            ''')


            
            # 创建过滤配置表 - 将 whitelist_overrides 改为 whitelist_required
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS filter_configs (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    description TEXT,
                    enabled BOOLEAN DEFAULT 1,
                    whitelist_required BOOLEAN DEFAULT 1,
                    created_at DATETIME NOT NULL
                )
            ''')
            
            # 检查是否需要迁移旧数据
            cursor.execute("PRAGMA table_info(filter_configs)")
            columns = [column[1] for column in cursor.fetchall()]
            
            # 如果存在旧字段但不存在新字段，进行迁移
            if 'whitelist_overrides' in columns and 'whitelist_required' not in columns:
                # 创建临时表
                cursor.execute('''
                    CREATE TABLE filter_configs_temp (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        name TEXT NOT NULL,
                        description TEXT,
                        enabled BOOLEAN DEFAULT 1,
                        whitelist_required BOOLEAN DEFAULT 1,
                        created_at DATETIME NOT NULL
                    )
                ''')
                
                # 复制数据，将 whitelist_overrides 的值反转后存入 whitelist_required
                cursor.execute('''
                    INSERT INTO filter_configs_temp (id, name, description, enabled, whitelist_required, created_at)
                    SELECT id, name, description, enabled, 1, created_at FROM filter_configs
                ''')
                
                # 删除旧表
                cursor.execute('DROP TABLE filter_configs')
                
                # 重命名临时表
                cursor.execute('ALTER TABLE filter_configs_temp RENAME TO filter_configs')
            

            
            # 修改为URL过滤配置表结构
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS url_filter_configs (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    description TEXT,
                    enabled BOOLEAN NOT NULL DEFAULT 1,
                    created_at DATETIME NOT NULL
                )
            ''')
            
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS url_blacklist (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    config_id INTEGER NOT NULL,
                    pattern TEXT NOT NULL,
                    description TEXT,
                    created_at DATETIME NOT NULL,
                    FOREIGN KEY (config_id) REFERENCES url_filter_configs (id) ON DELETE CASCADE
                )
            ''')
            

            
            conn.commit()
        
        # 迁移现有数据
        self.migrate_existing_data()

    def migrate_existing_data(self):
        """迁移现有数据"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            # 获取所有唯一的 execution_key
            cursor.execute('SELECT DISTINCT execution_key FROM search_results')
            execution_keys = cursor.fetchall()
            
            current_time = self.get_beijing_time()  # 获取北京时间
            
            # 为每个 execution_key 创建记录
            for (key,) in execution_keys:
                cursor.execute('SELECT 1 FROM executions WHERE execution_key = ?', (key,))
                if not cursor.fetchone():
                    cursor.execute('''
                        INSERT INTO executions (execution_key, created_at)
                        VALUES (?, ?)
                    ''', (key, current_time))
            
            conn.commit()

    def start_execution(self, execution_key, config_snapshot=None):
        """记录新的执行开始，支持配置快照"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                current_time = self.get_beijing_time()
                # 先检查是否已存在
                cursor.execute('SELECT 1 FROM executions WHERE execution_key = ?', (execution_key,))
                if not cursor.fetchone():
                    cursor.execute('''
                        INSERT INTO executions (execution_key, created_at, config_snapshot)
                        VALUES (?, ?, ?)
                    ''', (execution_key, current_time, config_snapshot))
                    conn.commit()
        except Exception as e:
            print(f"保存执行记录失败: {e}")
            raise

    def end_execution(self, execution_key, status='completed'):
        """记录执行结束"""
        with sqlite3.connect(self.db_file) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                UPDATE executions 
                SET end_time = ?, status = ?
                WHERE execution_key = ?
            ''', (datetime.now(), status, execution_key))
            conn.commit()

    def save_results(self, results, execution_key):
        """保存搜索结果"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            current_time = self.get_beijing_time()  # 获取北京时间
            
            # 确保 execution 记录存在
            cursor.execute('SELECT 1 FROM executions WHERE execution_key = ?', (execution_key,))
            if not cursor.fetchone():
                # 如果不存在，创建执行记录
                cursor.execute('''
                    INSERT INTO executions (execution_key, created_at)
                    VALUES (?, ?)
                ''', (execution_key, current_time))
            
            # 保存搜索结果
            for result in results:
                cursor.execute('''
                    INSERT INTO search_results 
                    (execution_key, title, full_title, link, abstract, publish_unit, search_url, wd, created_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    execution_key,
                    result.get('title', ''),
                    result.get('full_title', ''),
                    result.get('link', ''),
                    result.get('abstract', ''),
                    result.get('publish_unit', ''),
                    result.get('search_url', ''),
                    result.get('wd', ''),
                    current_time
                ))
            conn.commit()

    def get_execution_results(self, execution_key):
        """获取某次执行的所有结果"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT id, execution_key, title, full_title, link, 
                       abstract, publish_unit, search_url, wd, created_at
                FROM search_results 
                WHERE execution_key = ?
                ORDER BY created_at DESC
            ''', (execution_key,))
            return cursor.fetchall()

    def get_execution_status(self, execution_key):
        """获取执行状态"""
        with sqlite3.connect(self.db_file) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT status, total_results, start_time, end_time 
                FROM executions 
                WHERE execution_key = ?
            ''', (execution_key,))
            return cursor.fetchone()

    def get_recent_executions(self, limit=10):
        """获取最近的执行记录，包含配置快照"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT execution_key, created_at, config_snapshot
                FROM executions 
                ORDER BY created_at DESC 
                LIMIT ?
            ''', (limit,))
            return cursor.fetchall()

    def get_by_link(self, link):
        """根据链接获取结果"""
        with sqlite3.connect(self.db_file) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT * FROM search_results WHERE link = ?
                ''', (link,))
            return cursor.fetchall()

    def get_connection(self):
        """获取数据库连接"""
        return sqlite3.connect(self.db_file, detect_types=sqlite3.PARSE_DECLTYPES)

    def get_config(self, config_id):
        """获取单个配置"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('SELECT * FROM crawler_config WHERE id = ?', (config_id,))
            return cursor.fetchone()

    def get_all_configs(self):
        """获取所有配置"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('SELECT * FROM crawler_config ORDER BY created_at DESC')
            return cursor.fetchall()

    def get_beijing_time(self):
        """获取北京时间"""
        return (datetime.utcnow() + timedelta(hours=8)).strftime('%Y-%m-%d %H:%M:%S')

    def save_config(self, name, urls):
        """保存配置"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                current_time = self.get_beijing_time()
                cursor.execute(
                    'INSERT INTO crawler_config (name, urls, created_at) VALUES (?, ?, ?)',
                    (name, urls, current_time)
                )
                conn.commit()
                return cursor.lastrowid
        except Exception as e:
            print(f"保存配置失败: {e}")
            raise

    def save_execution(self, execution_key):
        """保存执行记录"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                current_time = self.get_beijing_time()
                cursor.execute(
                    'INSERT INTO executions (execution_key, created_at) VALUES (?, ?)',
                    (execution_key, current_time)
                )
                conn.commit()
        except Exception as e:
            print(f"保存执行记录失败: {e}")
            raise

    def save_search_results(self, results, execution_key):
        """保存搜索结果"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                current_time = self.get_beijing_time()

                for result in results:
                    cursor.execute('''
                        INSERT INTO search_results (
                            execution_key, title, full_title, link,
                            abstract, publish_unit, search_url, wd, created_at
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        execution_key,
                        result['title'],
                        result['full_title'],
                        result['link'],
                        result['abstract'],
                        result['publish_unit'],
                        result['search_url'],
                        result['wd'],
                        current_time
                    ))
                conn.commit()
        except Exception as e:
            print(f"保存搜索结果失败: {e}")
            raise

    # 定时任务相关方法
    def save_scheduled_task(self, name, config_id, schedule_type, schedule_config,
                           filter_config_id=None, url_filter_config_id=None,
                           skip_duplicate=True, skip_task_duplicate=True):
        """保存定时任务"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                current_time = self.get_beijing_time()
                cursor.execute('''
                    INSERT INTO scheduled_tasks (
                        name, config_id, schedule_type, schedule_config,
                        filter_config_id, url_filter_config_id,
                        skip_duplicate, skip_task_duplicate,
                        created_at, updated_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    name, config_id, schedule_type, schedule_config,
                    filter_config_id, url_filter_config_id,
                    skip_duplicate, skip_task_duplicate,
                    current_time, current_time
                ))
                conn.commit()
                return cursor.lastrowid
        except Exception as e:
            print(f"保存定时任务失败: {e}")
            raise

    def get_scheduled_tasks(self):
        """获取所有定时任务"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT st.*, cc.name as config_name
                    FROM scheduled_tasks st
                    LEFT JOIN crawler_config cc ON st.config_id = cc.id
                    ORDER BY st.created_at DESC
                ''')
                return cursor.fetchall()
        except Exception as e:
            print(f"获取定时任务失败: {e}")
            return []

    def get_scheduled_task(self, task_id):
        """获取单个定时任务"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT st.*, cc.name as config_name, cc.urls as config_urls
                    FROM scheduled_tasks st
                    LEFT JOIN crawler_config cc ON st.config_id = cc.id
                    WHERE st.id = ?
                ''', (task_id,))
                return cursor.fetchone()
        except Exception as e:
            print(f"获取定时任务失败: {e}")
            return None

    def update_scheduled_task_status(self, task_id, enabled):
        """更新定时任务状态"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                current_time = self.get_beijing_time()
                cursor.execute('''
                    UPDATE scheduled_tasks
                    SET enabled = ?, updated_at = ?
                    WHERE id = ?
                ''', (enabled, current_time, task_id))
                conn.commit()
        except Exception as e:
            print(f"更新定时任务状态失败: {e}")
            raise

    def delete_scheduled_task(self, task_id):
        """删除定时任务"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                # 删除任务
                cursor.execute('DELETE FROM scheduled_tasks WHERE id = ?', (task_id,))
                conn.commit()
        except Exception as e:
            print(f"删除定时任务失败: {e}")
            raise



    def get_filters(self):
        """获取所有过滤器设置"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        # 创建表（如果不存在）
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS title_blacklist (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            keyword TEXT UNIQUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        ''')
        
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS title_whitelist (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            keyword TEXT UNIQUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        ''')
        
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS filter_settings (
            id INTEGER PRIMARY KEY,
            settings TEXT,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        ''')
        
        # 获取黑名单
        cursor.execute('SELECT keyword FROM title_blacklist')
        blacklist = [row[0] for row in cursor.fetchall()]
        
        # 获取白名单
        cursor.execute('SELECT keyword FROM title_whitelist')
        whitelist = [row[0] for row in cursor.fetchall()]
        
        # 获取设置
        cursor.execute('SELECT settings FROM filter_settings WHERE id = 1')
        settings_row = cursor.fetchone()
        
        settings = {
            "enableTitleFilter": True,
            "whitelistOverridesBlacklist": True
        }
        
        if settings_row:
            try:
                import json
                settings = json.loads(settings_row[0])
            except:
                pass
        
        conn.close()
        
        return {
            "blacklist": blacklist,
            "whitelist": whitelist,
            "settings": settings
        }

    def add_to_blacklist(self, keyword):
        """添加关键词到黑名单"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('INSERT OR IGNORE INTO title_blacklist (keyword) VALUES (?)', (keyword,))
        
        conn.commit()
        conn.close()

    def remove_from_blacklist(self, keyword):
        """从黑名单移除关键词"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('DELETE FROM title_blacklist WHERE keyword = ?', (keyword,))
        
        conn.commit()
        conn.close()

    def add_to_whitelist(self, keyword):
        """添加关键词到白名单"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('INSERT OR IGNORE INTO title_whitelist (keyword) VALUES (?)', (keyword,))
        
        conn.commit()
        conn.close()

    def remove_from_whitelist(self, keyword):
        """从白名单移除关键词"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('DELETE FROM title_whitelist WHERE keyword = ?', (keyword,))
        
        conn.commit()
        conn.close()

    def save_filter_settings(self, settings):
        """保存过滤器设置"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        import json
        settings_json = json.dumps(settings)
        
        cursor.execute('''
        INSERT OR REPLACE INTO filter_settings (id, settings, updated_at)
        VALUES (1, ?, CURRENT_TIMESTAMP)
        ''', (settings_json,))
        
        conn.commit()
        conn.close()

    def get_all_filter_configs(self):
        """获取所有过滤配置"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('SELECT id, name, description, enabled, whitelist_required, created_at FROM filter_configs ORDER BY created_at DESC')
            return cursor.fetchall()

    def get_filter_config(self, config_id):
        """获取单个过滤配置及其关键词"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            # 获取配置基本信息
            cursor.execute('SELECT id, name, description, enabled, whitelist_required, created_at FROM filter_configs WHERE id = ?', (config_id,))
            config = cursor.fetchone()
            
            if not config:
                return None
            
            # 获取黑名单关键词 - 注意：这里应该使用实际存在的表
            # 由于blacklist_keywords表已被移除，这里需要使用新的表结构
            # 但由于filter_configs系统已经有了新的实现，这个方法可能需要重构
            blacklist = []
            whitelist = []
            
            # 构建完整配置
            config_dict = {
                'id': config[0],
                'name': config[1],
                'description': config[2],
                'enabled': bool(config[3]),
                'whitelist_required': bool(config[4]),
                'created_at': config[5],
                'blacklist': blacklist,
                'whitelist': whitelist
            }
            
            return config_dict

    def create_filter_config(self, name, description, enabled, whitelist_required):
        """创建新的过滤配置"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                current_time = self.get_beijing_time()

                # 插入配置基本信息
                cursor.execute('''
                    INSERT INTO filter_configs (name, description, enabled, whitelist_required, created_at)
                    VALUES (?, ?, ?, ?, ?)
                ''', (name, description, enabled, whitelist_required, current_time))

                config_id = cursor.lastrowid

                # 注意：blacklist_keywords和whitelist_keywords表已被移除
                # 如果需要使用过滤功能，请使用新的filter_blacklist和filter_whitelist表
                # 这些功能在web_gui.py中有完整的实现

                conn.commit()
                return config_id
        except Exception as e:
            print(f"创建过滤配置失败: {e}")
            raise

    def update_filter_config(self, config_id, name, description, enabled, whitelist_required):
        """更新过滤配置基本信息"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    UPDATE filter_configs
                    SET name = ?, description = ?, enabled = ?, whitelist_required = ?
                    WHERE id = ?
                ''', (name, description, enabled, whitelist_required, config_id))
                
                conn.commit()
                return True
        except Exception as e:
            print(f"更新过滤配置失败: {e}")
            raise

    def delete_filter_config(self, config_id):
        """删除过滤配置"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                # 删除配置及其关联的关键词
                cursor.execute('DELETE FROM filter_configs WHERE id = ?', (config_id,))
                
                conn.commit()
                return True
        except Exception as e:
            print(f"删除过滤配置失败: {e}")
            raise

    # 注意：blacklist_keywords和whitelist_keywords相关的方法已被移除
    # 请使用web_gui.py中实现的filter_blacklist和filter_whitelist功能

    def get_url_filter_configs(self):
        """获取所有URL过滤配置"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('SELECT id, name, description, enabled, created_at FROM url_filter_configs ORDER BY created_at DESC')
            return cursor.fetchall()

    def get_url_filter_config(self, config_id):
        """获取单个URL过滤配置及其规则"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            # 获取配置基本信息
            cursor.execute('SELECT id, name, enabled, created_at FROM url_filter_configs WHERE id = ?', (config_id,))
            config = cursor.fetchone()
            
            if not config:
                return None
            
            # 获取黑名单规则
            cursor.execute('SELECT id, pattern FROM url_blacklist WHERE config_id = ?', (config_id,))
            blacklist = cursor.fetchall()

            # 注意：url_whitelist表已被移除，因为实际功能中未使用

            return {
                'id': config[0],
                'name': config[1],
                'enabled': bool(config[2]),
                'created_at': config[3],
                'blacklist': [{
                    'id': b[0],
                    'pattern': b[1]
                } for b in blacklist]
            }

    def create_url_filter_config(self, name, description='', enabled=True):
        """创建URL过滤配置"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            current_time = self.get_beijing_time()
            cursor.execute(
                'INSERT INTO url_filter_configs (name, description, enabled, created_at) VALUES (?, ?, ?, ?)',
                (name, description, enabled, current_time)
            )
            conn.commit()
            return cursor.lastrowid

    def update_url_filter_config(self, config_id, name, description, enabled):
        """更新URL过滤配置"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute(
                'UPDATE url_filter_configs SET name = ?, description = ?, enabled = ? WHERE id = ?',
                (name, description, enabled, config_id)
            )
            conn.commit()

    def delete_url_filter_config(self, config_id):
        """删除URL过滤配置"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('DELETE FROM url_filter_configs WHERE id = ?', (config_id,))
            conn.commit()

    def add_url_blacklist(self, config_id, pattern, description=''):
        """添加URL黑名单规则"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            current_time = self.get_beijing_time()
            
            # 检查是否已存在相同的规则
            cursor.execute('''
                SELECT id FROM url_blacklist 
                WHERE config_id = ? AND pattern = ?
            ''', (config_id, pattern))
            
            if cursor.fetchone():
                raise Exception('该规则已存在')
            
            cursor.execute('''
                INSERT INTO url_blacklist (config_id, pattern, description, created_at)
                VALUES (?, ?, ?, ?)
            ''', (config_id, pattern, description, current_time))
            conn.commit()
            return cursor.lastrowid

    # 注意：url_whitelist相关的方法已被移除，因为实际功能中未使用URL白名单

    def batch_add_url_blacklist(self, config_id, patterns):
        """批量添加URL黑名单规则"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            current_time = self.get_beijing_time()
            
            # 获取现有规则
            cursor.execute('SELECT pattern FROM url_blacklist WHERE config_id = ?', (config_id,))
            existing_patterns = {row[0] for row in cursor.fetchall()}
            
            # 过滤掉已存在的规则
            new_patterns = [p for p in patterns if p and p not in existing_patterns]
            
            if new_patterns:
                # 批量添加规则
                values = [(config_id, pattern, '', current_time) for pattern in new_patterns]
                cursor.executemany(
                    'INSERT INTO url_blacklist (config_id, pattern, description, created_at) VALUES (?, ?, ?, ?)',
                    values
                )
                conn.commit()
            
            return len(new_patterns)

    def delete_url_blacklist(self, blacklist_id):
        """删除指定ID的URL黑名单规则"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('DELETE FROM url_blacklist WHERE id = ?', (blacklist_id,))
                conn.commit()
                return True
        except Exception as e:
            print(f"删除URL黑名单失败: {e}")
            return False

    def get_execution_config(self, execution_key):
        """获取某次执行的配置快照"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('SELECT config_snapshot FROM executions WHERE execution_key = ?', (execution_key,))
            row = cursor.fetchone()
            return row[0] if row else None