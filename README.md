# 百度搜索爬虫管理系统

## 项目简介

百度搜索爬虫管理系统是一个功能完整的智能化搜索数据采集平台，专门用于自动化采集百度搜索结果。系统提供了现代化的 Web 管理界面、强大的定时任务调度、智能验证码识别、数据去重过滤等企业级功能。

### 主要功能特性

-   **🚀 智能爬取引擎**：基于 Selenium 的智能浏览器自动化，支持反反爬虫机制
-   **🎯 精准数据提取**：自动提取标题、链接、摘要、发布单位等结构化数据
-   **🔄 定时任务调度**：支持 Cron 表达式和间隔调度的灵活定时任务系统
-   **🛡️ 智能验证码识别**：集成第三方验证码识别服务，自动处理滑块验证
-   **📊 数据管理系统**：完整的数据存储、查询、导出和历史记录管理
-   **🎛️ Web 管理界面**：现代化的响应式 Web 界面，支持实时日志监控
-   **🔍 高级过滤系统**：支持标题黑白名单、URL 过滤、重复数据检测
-   **📱 微信通知集成**：支持企业微信机器人实时状态通知
-   **📈 执行历史追踪**：完整的任务执行记录和配置快照功能
-   **💾 多格式数据导出**：支持 CSV 格式数据导出和批量下载

### 技术栈

-   **后端框架**：Flask 3.0.2 + Python 3.8+
-   **数据库**：SQLite3（轻量级，支持并发）
-   **浏览器自动化**：Selenium 4.18.1 + Chrome WebDriver
-   **任务调度**：APScheduler 3.10.4
-   **前端技术**：Bootstrap 5 + jQuery + 响应式设计
-   **图像处理**：Pillow 10.2.0
-   **HTTP 请求**：Requests 2.32.2
-   **HTML 解析**：BeautifulSoup4 4.12.2

## 中国大陆网络环境部署建议

> **如在中国大陆网络环境下部署，建议如下：**
>
> 1. 安装 uv 及依赖时，强烈建议使用国内 PyPI 镜像源，例如：
>
>     ```bash
>     pip install -i https://pypi.tuna.tsinghua.edu.cn/simple uv
>     uv pip install -r requirements.txt --index-url https://pypi.tuna.tsinghua.edu.cn/simple
>     ```
>
> 2. 若 chromedriver-autoinstaller 自动下载失败，请手动从 [清华镜像站](https://mirrors.tuna.tsinghua.edu.cn/chromedriver/) 下载与你本地 Chrome 版本对应的 ChromeDriver，并放到项目根目录或 PATH 路径下。
> 3. 或设置环境变量，令 chromedriver-autoinstaller 使用国内镜像：
>
>     - Linux/macOS:
>
>         ```bash
>         export CHROMEDRIVER_CDNURL="https://mirrors.tuna.tsinghua.edu.cn/chromedriver/"
>         ```
>
>     - Windows:
>
>         ```cmd
>         set CHROMEDRIVER_CDNURL=https://mirrors.tuna.tsinghua.edu.cn/chromedriver/
>         ```

## 项目结构

```
gc_baidu_crawler/
├── browser_search.py          # 核心爬虫引擎
├── web_gui.py                 # Web管理界面和API接口
├── database.py                # 数据库操作和管理
├── scheduler.py               # 定时任务调度器
├── config.py                  # 项目配置文件
├── config.example.py          # 配置文件示例
├── util.py                    # 工具函数库
├── start.py                   # 项目启动入口
├── requirements.txt           # Python依赖包列表
├── templates/                 # Web界面模板
│   └── index.html            # 主界面模板
├── static/                    # 静态资源文件
│   ├── css/                  # 样式文件
│   └── js/                   # JavaScript文件
├── logs/                      # 日志文件目录
├── save_data/                 # 爬取数据保存目录
└── crawler.db                # SQLite数据库文件
```

## 环境要求

-   **Python**：3.8 或以上版本
-   **Chrome 浏览器**：最新版本（系统已安装）
-   **操作系统**：Windows 10+、macOS 10.14+、Ubuntu 18.04+
-   **内存**：建议 4GB 以上
-   **磁盘空间**：至少 1GB 可用空间
-   **网络**：稳定的互联网连接

### 推荐工具

-   推荐使用 [uv](https://github.com/astral-sh/uv) 进行依赖和环境管理
-   推荐使用 [VS Code](https://code.visualstudio.com/) 进行代码编辑

## 快速部署步骤

### 1. 克隆项目

```bash
git clone <repository-url>
cd gc_baidu_crawler
```

### 2. 安装 Python 环境

请确保已安装 Python 3.8 或更高版本，并已添加到 PATH。
如未安装，请访问 [Python 官网](https://www.python.org/downloads/)。

### 3. 安装依赖管理工具

推荐使用 uv 进行依赖管理：

```bash
pip install uv
# 或使用pipx安装
pipx install uv
```

### 4. 安装项目依赖

```bash
# 使用uv安装（推荐）
uv pip install -r requirements.txt

# 或使用传统pip安装
pip install -r requirements.txt
```

> **注意**：本项目已集成 `chromedriver-autoinstaller`，首次运行时会自动下载并配置与本机 Chrome 版本匹配的 ChromeDriver，无需手动操作。

### 5. 配置项目

#### 5.1 创建配置文件

```bash
# 复制配置文件模板
cp config.example.py config.py
```

#### 5.2 编辑配置文件

打开 `config.py` 文件，根据需要修改以下配置：

```python
# 验证码识别服务配置
IMG_IDENTIFICATION_KEY = "your_key_here"

# 微信机器人配置
WECHAT = {
    'BOT_URL': 'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=your_key_here'
}

# 文件上传配置
UPLOAD = {
    'API_URL': 'https://your-api-endpoint.com/upload',
    'TOKEN': 'your_token_here',
    'TIMEOUT': 30
}
```

### 6. 启动系统

```bash
# 使用uv运行
uv run python start.py

# 或直接运行
python start.py
```

启动成功后，系统会自动打开浏览器访问 `http://127.0.0.1:5001`

## 详细使用说明

### Web 管理界面功能

系统提供了功能完整的 Web 管理界面，主要包含以下模块：

#### 1. 爬虫控制面板

-   **URL 配置**：支持批量输入百度搜索 URL
-   **实时日志**：显示爬虫运行状态和详细日志
-   **启动/停止控制**：一键启动或停止爬虫任务
-   **进度监控**：实时显示爬取进度和结果统计

#### 2. 数据管理

-   **搜索结果查看**：分页浏览所有爬取的数据
-   **高级搜索**：支持按标题、链接、执行时间等条件筛选
-   **数据导出**：支持 CSV 格式批量导出
-   **历史记录**：查看所有执行记录和配置快照

#### 3. 过滤配置

-   **标题过滤**：支持黑白名单配置，精确控制数据质量
-   **URL 过滤**：支持 URL 级别的过滤规则
-   **去重设置**：支持全局去重和任务内去重

#### 4. 定时任务

-   **任务调度**：支持 Cron 表达式和间隔调度
-   **任务管理**：创建、编辑、删除定时任务
-   **执行监控**：查看定时任务执行状态和历史

### API 接口文档

系统提供了完整的 RESTful API 接口，支持程序化调用：

#### 核心接口

**1. 启动爬虫任务**

```http
POST /api/start
Content-Type: application/json

{
    "urls": ["百度搜索URL列表"],
    "skipDuplicate": true,
    "skipTaskDuplicate": true,
    "filterConfigId": 1,
    "urlFilterConfigId": 1
}
```

**2. 停止爬虫任务**

```http
POST /api/stop
```

**3. 获取实时日志**

```http
GET /api/logs?last_id=0
```

**4. 查询搜索结果**

```http
GET /api/search_results?page=1&per_page=20&title=关键词
```

**5. 下载执行结果**

```http
GET /api/download/<execution_key>
```

#### 配置管理接口

**1. 保存 URL 配置**

```http
POST /api/save_config
Content-Type: application/json

{
    "name": "配置名称",
    "urls": ["URL列表"]
}
```

**2. 获取配置列表**

```http
GET /api/configs
```

**3. 删除配置**

```http
DELETE /api/config/<config_id>
```

#### 定时任务接口

**1. 创建定时任务**

```http
POST /api/scheduled_tasks
Content-Type: application/json

{
    "name": "任务名称",
    "config_id": 1,
    "schedule_type": "cron",
    "schedule_config": {"cron": "0 9 * * *"},
    "filter_config_id": 1,
    "url_filter_config_id": 1
}
```

**2. 获取任务列表**

```http
GET /api/scheduled_tasks
```

**3. 删除任务**

```http
DELETE /api/scheduled_task/<task_id>
```

## 配置参数详细说明

### 核心配置 (config.py)

#### 验证码识别配置

```python
# 验证码识别服务密钥
IMG_IDENTIFICATION_KEY = "your_key_here"
```

#### 百度搜索配置

```python
BAIDU = {
    'BASE_URL': 'https://www.baidu.com/s?',  # 百度搜索基础URL
    'SEARCH_TIME_RANGE': {
        'DAYS': 3  # 搜索最近几天的数据
    },
    'SELECTORS': {
        'RESULT': {
            'CONTAINER': 'div.result.c-container',  # 搜索结果容器
            'TITLE': 'h3.c-title a',  # 标题元素
            'ABSTRACT': '.content-right_1THTn',  # 摘要元素
            'PUBLISH_UNIT': 'div.source_1Vdff span.c-color-gray',  # 发布单位
            'TOOLS': 'c-tools'  # 工具栏元素
        },
        'PAGINATION': {
            'CONTAINER': 'page',  # 分页容器ID
            'NEXT_BUTTON': '#page a.n'  # 下一页按钮
        },
        'VERIFICATION': {
            'SLIDER': 'passMod_dialog-mask'  # 滑块验证码class
        }
    }
}
```

#### 文件处理配置

```python
FILE = {
    'CSV_FIELDS': [
        'title',         # 标题
        'full_title',    # 全标题
        'link',          # 实际链接
        'abstract',      # 摘要
        'publish_unit',  # 发布单位
        'search_url',    # 搜索的链接
        'wd'            # 搜索的关键字
    ],
    'CSV_HEADERS': {    # 中文表头映射
        'title': '标题',
        'full_title': '全标题',
        'link': '实际链接',
        'abstract': '摘要',
        'publish_unit': '发布单位',
        'search_url': '搜索的链接',
        'wd': '搜索的关键字'
    }
}
```

#### 上传配置

```python
UPLOAD = {
    'API_URL': 'https://your-api-endpoint.com/upload',  # 上传API地址
    'TOKEN': 'your_token_here',  # 认证令牌
    'TIMEOUT': 30  # 上传超时时间(秒)
}
```

#### 微信机器人配置

```python
WECHAT = {
    'BOT_URL': 'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=your_key_here'
}
```

### 爬虫行为配置

#### 去重设置

-   **skip_duplicate**: 全局去重，避免重复爬取相同 URL 的数据
-   **skip_task_duplicate**: 任务内去重，同一次任务中避免重复数据

#### 过滤配置

-   **标题过滤**: 支持黑白名单，精确控制数据质量
-   **URL 过滤**: 支持 URL 级别的过滤规则
-   **发布单位过滤**: 可根据发布单位进行筛选

## 运行示例和常见用法

### 1. 基础爬取示例

**通过 Web 界面操作：**

1. 访问 `http://127.0.0.1:5001`
2. 在 URL 配置区域输入百度搜索 URL
3. 配置过滤选项（可选）
4. 点击"开始爬取"按钮
5. 在实时日志区域监控进度
6. 爬取完成后在"搜索结果"页面查看数据

**通过 API 调用：**

```bash
curl -X POST http://127.0.0.1:5001/api/start \
  -H "Content-Type: application/json" \
  -d '{
    "urls": [
      "https://www.baidu.com/s?wd=关键词1",
      "https://www.baidu.com/s?wd=关键词2"
    ],
    "skipDuplicate": true,
    "skipTaskDuplicate": true
  }'
```

### 2. 定时任务示例

**创建每日定时任务：**

```json
{
    "name": "每日新闻爬取",
    "config_id": 1,
    "schedule_type": "cron",
    "schedule_config": { "cron": "0 9 * * *" },
    "filter_config_id": 1,
    "skip_duplicate": true,
    "skip_task_duplicate": true
}
```

**创建间隔任务：**

```json
{
    "name": "每小时监控",
    "config_id": 2,
    "schedule_type": "interval",
    "schedule_config": { "hours": 1 },
    "filter_config_id": 1
}
```

### 3. 数据导出示例

**导出特定执行的结果：**

```bash
curl -O http://127.0.0.1:5001/api/download/EXEC_20250615_102244_B054D
```

**批量查询数据：**

```bash
curl "http://127.0.0.1:5001/api/search_results?page=1&per_page=50&title=关键词"
```

## 数据库结构说明

系统使用 SQLite3 数据库存储所有数据，主要包含以下表结构：

### 核心数据表

#### 1. executions（执行记录表）

```sql
CREATE TABLE executions (
    execution_key TEXT PRIMARY KEY,    -- 执行唯一标识
    created_at DATETIME NOT NULL,      -- 创建时间
    config_snapshot TEXT               -- 配置快照（JSON格式）
);
```

#### 2. search_results（搜索结果表）

```sql
CREATE TABLE search_results (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    execution_key TEXT NOT NULL,       -- 关联执行记录
    title TEXT,                        -- 标题
    full_title TEXT,                   -- 完整标题
    link TEXT,                         -- 链接地址
    abstract TEXT,                     -- 摘要内容
    publish_unit TEXT,                 -- 发布单位
    search_url TEXT,                   -- 搜索URL
    wd TEXT,                          -- 搜索关键词
    created_at DATETIME NOT NULL,      -- 创建时间
    FOREIGN KEY (execution_key) REFERENCES executions (execution_key)
);
```

#### 3. crawler_config（爬虫配置表）

```sql
CREATE TABLE crawler_config (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,                -- 配置名称
    urls TEXT NOT NULL,                -- URL列表（JSON格式）
    created_at DATETIME NOT NULL       -- 创建时间
);
```

#### 4. scheduled_tasks（定时任务表）

```sql
CREATE TABLE scheduled_tasks (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,                -- 任务名称
    config_id INTEGER NOT NULL,        -- 关联配置ID
    schedule_type TEXT NOT NULL,       -- 调度类型（cron/interval）
    schedule_config TEXT NOT NULL,     -- 调度配置（JSON格式）
    filter_config_id INTEGER,          -- 过滤配置ID
    url_filter_config_id INTEGER,      -- URL过滤配置ID
    skip_duplicate BOOLEAN DEFAULT 1,  -- 是否跳过重复
    skip_task_duplicate BOOLEAN DEFAULT 1, -- 是否跳过任务内重复
    is_active BOOLEAN DEFAULT 1,       -- 是否激活
    created_at DATETIME NOT NULL,      -- 创建时间
    FOREIGN KEY (config_id) REFERENCES crawler_config (id)
);
```

#### 5. filter_configs（过滤配置表）

```sql
CREATE TABLE filter_configs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,                -- 配置名称
    type TEXT NOT NULL,                -- 过滤类型（title/url）
    whitelist TEXT,                    -- 白名单（JSON格式）
    blacklist TEXT,                    -- 黑名单（JSON格式）
    settings TEXT,                     -- 其他设置（JSON格式）
    created_at DATETIME NOT NULL       -- 创建时间
);
```

### 数据关系说明

-   **executions** ← **search_results**：一对多关系，一次执行可产生多条搜索结果
-   **crawler_config** ← **scheduled_tasks**：一对多关系，一个配置可被多个定时任务使用
-   **filter_configs** ← **scheduled_tasks**：多对多关系，任务可使用多种过滤配置

## 故障排除和常见问题

### 1. 安装和启动问题

**问题：Chrome 浏览器或 ChromeDriver 相关错误**

```
解决方案：
1. 确保已安装最新版Chrome浏览器
2. 检查chromedriver-autoinstaller是否正常工作
3. 手动下载对应版本的ChromeDriver到项目目录
4. 设置环境变量CHROMEDRIVER_CDNURL使用国内镜像
```

**问题：Python 依赖安装失败**

```
解决方案：
1. 使用国内PyPI镜像源：pip install -i https://pypi.tuna.tsinghua.edu.cn/simple
2. 升级pip版本：python -m pip install --upgrade pip
3. 使用uv进行依赖管理：uv pip install -r requirements.txt
4. 检查Python版本是否为3.8+
```

**问题：端口占用或无法访问 Web 界面**

```
解决方案：
1. 检查5001端口是否被占用：netstat -an | grep 5001
2. 修改start.py中的端口配置
3. 检查防火墙设置
4. 确保浏览器可以访问localhost
```

### 2. 爬取过程问题

**问题：验证码识别失败**

```
解决方案：
1. 检查IMG_IDENTIFICATION_KEY配置是否正确
2. 确认验证码识别服务余额充足
3. 查看日志中的详细错误信息
4. 尝试手动处理验证码或更换识别服务
```

**问题：爬取速度过慢或被反爬虫**

```
解决方案：
1. 适当增加请求间隔时间
2. 使用代理IP池（需要自行实现）
3. 调整浏览器User-Agent和其他请求头
4. 分批次进行爬取，避免频繁请求
```

**问题：数据提取不完整或格式错误**

```
解决方案：
1. 检查config.py中的CSS选择器是否正确
2. 查看百度搜索页面结构是否发生变化
3. 更新选择器配置以适应新的页面结构
4. 检查网络连接和页面加载时间
```

### 3. 数据和存储问题

**问题：数据库文件损坏或无法访问**

```
解决方案：
1. 检查crawler.db文件权限
2. 备份数据库文件并尝试修复
3. 重新初始化数据库（会丢失历史数据）
4. 检查磁盘空间是否充足
```

**问题：CSV 导出文件乱码**

```
解决方案：
1. 使用支持UTF-8的编辑器打开CSV文件
2. 在Excel中导入时选择UTF-8编码
3. 检查系统区域设置
4. 使用专业的CSV查看工具
```

### 4. 定时任务问题

**问题：定时任务不执行或执行异常**

```
解决方案：
1. 检查任务配置的Cron表达式是否正确
2. 查看系统时间和时区设置
3. 检查任务是否处于激活状态
4. 查看调度器日志获取详细错误信息
```

### 5. 性能优化建议

**内存优化：**

-   定期清理日志文件
-   控制单次爬取的数据量
-   适当调整浏览器参数

**存储优化：**

-   定期备份和清理历史数据
-   使用数据库索引提高查询效率
-   考虑使用更高性能的数据库（如 PostgreSQL）

**网络优化：**

-   使用稳定的网络连接
-   配置合适的超时时间
-   考虑使用 CDN 加速静态资源

## 开发和贡献指南

### 开发环境搭建

1. **克隆项目并安装依赖**

```bash
git clone <repository-url>
cd gc_baidu_crawler
uv pip install -r requirements.txt
```

2. **配置开发环境**

```bash
# 复制配置文件
cp config.example.py config.py

# 安装开发工具（可选）
pip install black flake8 pytest
```

3. **运行测试**

```bash
# 运行单元测试
python -m pytest

# 代码格式化
black .

# 代码检查
flake8 .
```

### 项目架构说明

#### 核心模块

-   **browser_search.py**: 爬虫核心引擎，负责浏览器自动化和数据提取
-   **web_gui.py**: Web 界面和 API 服务，提供用户交互接口
-   **database.py**: 数据库操作层，封装所有数据库相关操作
-   **scheduler.py**: 任务调度器，管理定时任务的执行
-   **util.py**: 工具函数库，提供通用功能支持

#### 设计模式

-   **单例模式**: Database 类确保数据库连接的唯一性
-   **观察者模式**: 日志系统支持多个观察者接收日志事件
-   **策略模式**: 验证码处理支持多种识别策略
-   **工厂模式**: 任务调度器根据类型创建不同的触发器

### 贡献指南

#### 代码规范

-   使用 Python PEP 8 编码规范
-   函数和类需要添加详细的文档字符串
-   重要功能需要编写单元测试
-   提交前运行代码格式化和检查工具

#### 提交流程

1. Fork 项目到个人仓库
2. 创建功能分支：`git checkout -b feature/new-feature`
3. 提交代码：`git commit -m "Add new feature"`
4. 推送分支：`git push origin feature/new-feature`
5. 创建 Pull Request

#### 问题报告

-   使用 GitHub Issues 报告问题
-   提供详细的错误信息和复现步骤
-   包含系统环境和版本信息
-   附上相关的日志文件

### 扩展开发

#### 添加新的验证码处理器

```python
# 在verification/handlers/目录下创建新的处理器
class NewCaptchaHandler:
    def handle(self, image_data):
        # 实现验证码识别逻辑
        pass
```

#### 添加新的数据导出格式

```python
# 在util.py中添加新的导出函数
def export_to_excel(data, filename):
    # 实现Excel导出逻辑
    pass
```

#### 添加新的通知方式

```python
# 扩展通知功能
class EmailNotifier:
    def send_notification(self, message):
        # 实现邮件通知逻辑
        pass
```

## 许可证信息

本项目采用 MIT 许可证，详情请参阅 [LICENSE](LICENSE) 文件。

### 免责声明

-   本工具仅供学习和研究使用
-   使用者需遵守相关网站的 robots.txt 和服务条款
-   请合理控制爬取频率，避免对目标网站造成负担
-   使用本工具产生的任何法律责任由使用者自行承担

### 技术支持

-   **文档**: 详细文档请参阅本 README 文件
-   **问题反馈**: 请通过 GitHub Issues 提交问题
-   **功能建议**: 欢迎通过 Issues 或 Pull Request 提出改进建议
-   **社区讨论**: 欢迎在项目讨论区分享使用经验

---

**最后更新时间**: 2025 年 6 月 15 日
**版本**: v1.0.0
**维护状态**: 积极维护中
